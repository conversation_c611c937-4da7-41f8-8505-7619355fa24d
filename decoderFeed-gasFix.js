const WebSocket = require('ws');
const net = require('net'); // Ajout pour socket TCP
const { ethers } = require('ethers');
const { performance } = require('perf_hooks');
const { Buffer } = require('buffer');
require('log-timestamp');

const fs = require('fs');
const path = require('path');

// Unwanted method IDs
const alwaysUnwantedMethodIds = new Set([
    '0x', // No calldata
    '0xa9059cbb', // ERC20 transfer
    '0x095ea7b3', // Approve
    '0x1a5577b2', // Receive BNB
    '0xb143044b',// Execute
    '0x3dec3f7c', // St TK
    '0xc7c7f5b3', // Send
    '0xe7139c8a', // Send // 1x
    '0x5648d3ac', // Claim
    '0x725f8e55', // Claim 1x
    '0x3158952e', // Claim 1x
    '0xc9807539', // Transmit
    '0x2e1a7d4d', // Withdraw
    '0x8e19899e', // Withdraw 1x,
    '0xc358547e', //  Withdraw 1x,
    '0xc30d8910'//  Execute Deposit
]);

const conditionalUnwantedMethodIds = new Map([
    // Example: Skip '0xa9059cbb' only on this contract
    [['0xf6326fb3', '******************************************'], true], //deposit ETH
    [['0x704c17ab', '******************************************'], true], //send to account
    [['0xba677db7', '******************************************'], true], //UnifiedRouterV2 start method
    [['0x1a98b2e0', '******************************************'], true], // method executeWithToken
    [['0x', '******************************************'], true], // Transfer method only

]);

const alwaysUnwantedToAddresses = new Set([
    '******************************************',
    '******************************************',
    '******************************************',
    '******************************************', // Hyperliquid: Deposit Bridge 2
    '******************************************', // Axelar Network : Gateway
    '******************************************', // Across Protocol : Bridge
    '******************************************', // Relay Receiver
    '******************************************', // OpenSea SeaPort 1.6
    '******************************************', // Risk Oracle contract that publishes bulk risk parameters updates
    '******************************************',
    '******************************************', // Bulk Tasks
    '******************************************', // TransparentUpgradeablePRoxy
    '******************************************', // ERC1967Proxy
    '0x9b51ef044d3486a1fb0a2d55a6e0ceeadd323e66', // TransparentUpgradeablePRoxy -> Bulk Submit solutions
    '0x3169844a120c0f517b4eb4a750c08d8518c8466a', // Proxy commitBlocks
    '0xa56148f44d8f2018cb1ae80213df0daf9ddcd4ec',
    '0xbbee07b3e8121227afcfe1e2b82772246226128e', // Vertex Protocl Endpoint
    '0x9a926eafe5cf9d1d9e077a968c8a1fb3223b321c',
    '0x31cae3b7fb82d847621859fb1585353c5720660d', // OptimizedTransparentUpgradeableProxy
    '0xd152f549545093347a162dce210e7293f1452150', // Disperse.app
    '0xcd36fff23d57a2c8777b7a5999689de5b2de5834',
    '0xdc7dcfeb77f2d2b9b27ed07dc168031850fcec52', // U contract
    '******************************************',
    '0x517fff15506e037ea6b367b89fe131d28f72001e', // TransparentUpgradeableProxy -> Claim
    '0xb28423ff82f16a27fdf96784f9bf90ab64851b6b',
    '0x602b805eedddbbd9ddff44a7dcbd46cb07849685', // ExchangeRouter
    '0x1195cf65f83b3a5768f3c496d3a05ad6412c64b7',
    '0xc0c6698e11a7dd3d194cf8f57aa53f57b0cc56da', // U address 
    '0xd25ee550f5eaeca2cc6be70b240d1dbf7d11d77c',
    '0x3872eb3c806f66c0cd6206a911ab43c2c573f9c3', // fais des swap sans events
    '0xc7bbb998796c716c7a2be5bb5feef2f6a14bd9ac',
    '0xbb870fd5b14cd37be7c3a891396b59bf77967885',
    '0xd5b450646037a9d98f4a6705b2d0d1da44e629d0', // votes
    '0x916cd9a93277a2db5d75a44695ebf685e9b67009',
    '0x09b0acb5522131d9dcf3dd39a5da7f0d284360e5',
    '0x060331feda35691e54876d957b4f9e3b8cb47d20',
    '0xd62376858092caa10df2a5a3bc3a1df746b75f08',
    '0xa2495fde97e2c1a913b250db97be67223c175bdb', // lié à Moongate Deployer
    '0x86be76a0fa2bd3ecb69330cbb4fd1f62c48f43e3', // commitStore
    '0x8278e011400772f1be24d5474296b3d1133f6eea',
    '0xfd41041180571c5d371bea3d9550e55653671198', // Xai deployer
    '0x2e5dc67a12cdcf80124e0d2ba01b7a9f04de04c6', // set Prices With Bits
    '0xcb8c721aa85c9aaa02bb11ad4e75e74ee163a9be', // Clone -> 0x7a20faef
    '0x3a0d9b80083410034318e79f838d6c78c1532f10', // Start Close Game
    '0xb5296a1d8fc57bd0af3375c68d48114e91f3b1ad', // earn collect rewards
    '0x7fb3614d4aae98f91b02a09945405a8d77bcceaf', // Claim
    '0xae447c1f7d2d504ad6dce2dd28f35aa0f1bff9cf', //playroom game
    '0xee116128b9aaadbcd1f7c18608c5114f594cf5d6', // Handlers Limit Trade
    '0x6a065083886ec63d274b8e1fe19ae2ddf498bfdd', // Orbiter Finance Bridge 3
    '0x070af5bce820f61e409417aff9b4c1b15b6537df',
    '0x39b875c5e3fc605051a5d9ae1af0dc965b1d20b5', // Batch Transfers
    '0x02d5251018c6fde7bbef8412585714fa7c1df3ac', // CATCH token stuff
    '0xf00fcc646ffb606cb0dc939b66b8f2b563031e6e',
    '0x0f5e11591c64163f04a448403dd59d6849ee4f0c',
    '0x0020320ee721b5a90ebaa325d8b1b2d22cf5de6e',
    '0x8ee250324beb641754b1fbd081878e6844523ecf',
    '0x2fe2cbfad87a7bcd0f0920ec15a9d08badd7561e',
    '0x0e9c1a3aa696299e38b00a8144bf6dc16c1f5400',
    '0x0305dde0e4781430acb40dd2fe5e44651aec6e21',
    '0x8d5f7156eb384902b71ae991c563bb9ee47e0d68',
    '0x391e7c679d29bd940d63be94ad22a25d25b5a604',
    '0x2c5d06f591d0d8cd43ac232c2b654475a142c7da',
    '0x37d9dc70bfcd8bc77ec2858836b923c560e891d1',
    '0x177d36dbe2271a4ddb2ad8304d82628eb921d790',
    '0x000000000001467a230d332f218187ffafb8ec0f',
    '0x1f419f59f2fd13d418639dd8c348b7e436137c7c',
    //Tokens
    '0x82af49447d8a07e3bd95bd0d56f35241523fbab1',
    '0xaf88d065e77c8cc2239327c5edb3a432268e5831'
]);
let transactionsData = []; // contient les txs pour chaque block

// ===== SOCKET VERS RUST SIMULATOR =====
let rustSocket = null;

function connectToRustSimulator() {
    rustSocket = new WebSocket('ws://127.0.0.1:9999');
    
    rustSocket.onopen = () => {
        console.log('🔌 Connecté au simulateur Rust WebSocket sur port 9999');
    };
    
    rustSocket.onmessage = (event) => {
        try {
            const result = JSON.parse(event.data);
            console.log(`📊 Résultats Rust Block ${result.block_number}:`);
            console.log(`   ✅ Succès: ${result.successful_transactions}/${result.total_transactions}`);
            console.log(`   ⚡ Moyenne: ${result.average_time_us.toFixed(1)}µs par TX`);
            console.log(`   🕒 Total: ${result.total_time_ms.toFixed(2)}ms`);
        } catch (e) {
            console.error('❌ Erreur parsing résultat Rust:', e.message);
        }
    };
    
    rustSocket.onclose = () => {
        console.log('🔌 Connexion Rust fermée, reconnexion dans 5s...');
        setTimeout(connectToRustSimulator, 5000);
    };
    
    rustSocket.onerror = (err) => {
        console.error('❌ Erreur WebSocket Rust:', err.message);
    };
}

// Connecter au démarrage
connectToRustSimulator();

class FeedRelayTracker {
    constructor() {
        // // Connect to your full node (assumed to be running locally on EC2)
        // this.provider = new ethers.JsonRpcProvider('http://localhost:8547', { // 8545 pour Anvil (sinon 8547)
        // chainId: 42161,
        // name: 'arbitrum-one'
        // });

        this.wsProvider = new ethers.WebSocketProvider('wss://lingering-flashy-lambo.arbitrum-mainnet.quiknode.pro/3b4184e83918c9bb09a12af99065f77188c85fd6/', {
        chainId: 42161,
        name: 'arbitrum-one'
        });
        

        // Connect to the local feed relay
        this.ws = new WebSocket('ws://localhost:9642');

        // Genesis block offset for Arbitrum One
        this.currentBlockNumber = null;
        this.genesisBlockOffset = 22207817;
        this.lastSequenceNumber = null;
        this.newFeedTime = null;
        this.sequenceToBlock = new Map();
        this.alwaysUnwantedMethodIds = alwaysUnwantedMethodIds;
        this.conditionalUnwantedMethodIds = conditionalUnwantedMethodIds;

        // Load all decoder scripts from the 'decoders' folder
        this.currentMsgPairs = new Map(); // store les pools par l2Msg
    }

    

   async startListening() {
        this.ws.on('open', () => {
            console.log('Connected to local feed relay at ws://localhost:9642');
        });

        this.ws.on('message', async (data) => {
            try {
                // Decompress the message if it's compressed (feed relay might send compressed data)
                const step0 = performance.now();
                const decompressedData = Buffer.from(data).toString('utf8');
                const message = JSON.parse(decompressedData)
                if(message.messages.length != 1) {
                    console.log(`length is ${message.messages.length} so we skip`)
                    return;
                }
                //console.log("message",message)
                const messageInfo = message.messages[0].message.message;
                const l2Msg = messageInfo.l2Msg;
                //console.log("l2Msg",l2Msg)
                //messageInfo.header.blockNumber : c'est le blockNumber L1
                if(messageInfo.header.kind !==3) {
                    console.log(`WTF header.kind pas 3`,messageInfo.header)
                    return;
                }

                const sequenceNumber = message.messages[0].sequenceNumber;
                this.lastSequenceNumber = sequenceNumber;
                const calculatedBlockNumber = sequenceNumber + this.genesisBlockOffset;
                //this.currentBlockNumber = calculatedBlockNumber; // Moved up

                const blockDifference = this.currentBlockNumber - calculatedBlockNumber;
                this.newFeedTime = performance.now();
                //const newFeedDate = new Date(Date.now()).toISOString();
                console.log(`L2Msg Block: ${calculatedBlockNumber}`);
                console.log(`Latest new Block: ${this.currentBlockNumber}`);
                //console.log(`Block Difference: ${blockDifference} blocks`);
                console.log('---');

                // If block difference is small (e.g., <10), it's real-time
                if (blockDifference < 10) {
                    console.log('Feed relay is providing near real-time data!');
                } 
                //else {console.warn('Feed relay still delayed by', blockDifference, 'blocks');}

                // Here you can integrate your existing log decoding logic
                // Example: process message.messages[0].message.l2Msg with your script
                const decodedBuffer = ethers.decodeBase64(l2Msg);                   
                const messageType = decodedBuffer[0];
                //console.log("decodedBuffer (hex):", decodedBuffer.toString('hex')); // Full hex string

                const txData = decodedBuffer.slice(1); // on enlève le 3 ou 4 parfois...
                const debut2 = performance.now();
                //console.log(`1er partie ${debut2-step0} ms`) // < 0.8ms

                this.currentMsgPairs.clear();
                if (messageType === 4) {
                    await this.processTransaction(txData);
                    //console.log(`Temps pour 1 segment`,performance.now()-debut2)

                } else if (messageType === 3) {
                    //const step1 = performance.now();
                    const segments = this.extractSegments(txData);
                    //console.log(`Extraction segments ${performance.now()-step1} ms`) // < 0.6ms
                    for (const segment of segments) {
                        // if (segment.type === 131) {
                        //     console.log(`Type ${segment.type}, ON NE TRAITE PAS. raw data: ${segment.data.toString('hex')}`);
                        // }                       
                        await this.processTransaction(Uint8Array.from(segment.data.slice(2)));
                    }
                    console.log(`Temps pour ${segments.length} segments`,performance.now()-debut2)
                }
                this.printAggregatedPairs(calculatedBlockNumber);
                transactionsData = [];
            } catch (error) {
                console.error('Error processing feed message:', error);
            }
        });

        this.ws.on('error', (error) => {
            console.error('WebSocket error:', error);
        });

        this.ws.on('close', () => {
            console.log('Disconnected from feed relay');
        });
    }

    extractSegments(dataToDecode) {
         // Find all delimiters (6 zeros + type, not preceded by 0)
         const delimiterPositions = [];
         let lastPosition = 7;
         for (let i = 1; i < dataToDecode.length - 6; i++) { // Start at 1 to check byte before
             if (
                 (dataToDecode[i - 1] !== 0 || dataToDecode[i - 2] !== 0) && // Byte before must not be 0
                 dataToDecode[i] === 0 &&
                 dataToDecode[i + 1] === 0 &&
                 dataToDecode[i + 2] === 0 &&
                 dataToDecode[i + 3] === 0 &&
                 dataToDecode[i + 4] === 0 &&
                 dataToDecode[i + 5] === 0 &&
                 dataToDecode[i + 7] !== 0 && //c'est la lenght pour les type 0 aussi
                 dataToDecode[i + 8] === 4 
                 //&& (dataToDecode[i + 9] == 248 || dataToDecode[i + 9] == 249 || dataToDecode[i + 10] == 248 || dataToDecode[i + 10] == 249)
             ) {
                 // console.log("dataToDecode[i + 9]",dataToDecode[i + 9])
                 // console.log("dataToDecode[i + 10]",dataToDecode[i + 10])

                 // Check if segment from last position to this one is at least 55 bytes
                 const segmentLength = i - lastPosition;
                 if (segmentLength >= 55) {
                     delimiterPositions.push(i);
                     lastPosition = i + 7; // Update last valid position to after type byte
                 }
             }
         }

         // Step 3: Extract segments (from after type byte to next delimiter or end)
         const segments = [];
         let offset = 7; //on commence après le 1er delimiter
         while (offset < dataToDecode.length) {
             const type = dataToDecode[offset - 1]; // Type byte before segment
             let segmentData;
             if (type === 0) {
                 const length = dataToDecode[offset]; // First byte after type is length
                 segmentData = dataToDecode.slice(offset, offset + length + 1); // Include length byte
                 offset += length + 1 + 7; // Skip segment + next delimiter (6 zeros + type)
             } else {
                 const nextDelimiter = delimiterPositions.find(pos => pos > offset - 7) || dataToDecode.length;
                 segmentData = dataToDecode.slice(offset, nextDelimiter);
                 offset = nextDelimiter + 7; // Move to after next delimiter
             }
             if (segmentData.length > 0) {
                 segments.push({ type, data: segmentData });
             }
         }
         return segments ;
    }

    serializeTransaction(txData) {
        console.log(`DEBUG: serializeTransaction, txData length=${txData.length}, hex=${txData.toString('hex')}`);
        let idx = 0;
        const fields = [];
        while (idx < txData.length) {
            console.log(`DEBUG: idx=${idx}, byte=0x${txData[idx]?.toString(16) || 'undefined'}`);
            if (txData[idx] < 0x80) {
                fields.push(txData[idx]);
                idx++;
            } else if (txData[idx] <= 0xb7) {
                const len = txData[idx] - 0x80;
                if (idx + 1 + len > txData.length) {
                    console.error(`DEBUG: Invalid short string len=${len} at idx=${idx}`);
                    return null;
                }
                fields.push(txData.slice(idx + 1, idx + 1 + len));
                idx += 1 + len;
            } else if (txData[idx] <= 0xbf) {
                const lenBytes = txData[idx] - 0xb7;
                if (idx + 1 + lenBytes > txData.length) {
                    console.error(`DEBUG: Invalid long string lenBytes=${lenBytes} at idx=${idx}`);
                    return null;
                }
                const len = parseInt(txData.slice(idx + 1, idx + 1 + lenBytes).toString('hex'), 16);
                if (idx + 1 + lenBytes + len > txData.length) {
                    console.error(`DEBUG: Invalid long string data len=${len} at idx=${idx}`);
                    return null;
                }
                fields.push(txData.slice(idx + 1 + lenBytes, idx + 1 + lenBytes + len));
                idx += 1 + lenBytes + len;
            } else if (txData[idx] === 0xc0) {
                fields.push(Buffer.from([]));
                idx++;
            } else {
                console.error(`DEBUG: Invalid RLP byte=0x${txData[idx]?.toString(16)} at idx=${idx}`);
                return null;
            }
        }
        console.log(`DEBUG: fields count=${fields.length}`);
    
        const txFields = {
            chainId: fields[0]?.length ? parseInt(fields[0].toString('hex'), 16) : 0,
            nonce: fields[1]?.length ? parseInt(fields[1].toString('hex'), 16) : 0,
            maxPriorityFeePerGas: fields[2]?.length ? parseInt(fields[2].toString('hex'), 16) : 0,
            maxFeePerGas: fields[3]?.length ? parseInt(fields[3].toString('hex'), 16) : 0,
            gasLimit: fields[4]?.length ? parseInt(fields[4].toString('hex'), 16) : 0,
            to: fields[5]?.length ? `0x${fields[5].toString('hex')}` : null,
            value: fields[6]?.length ? parseInt(fields[6].toString('hex'), 16) : 0,
            data: fields[7]?.length ? `0x${fields[7].toString('hex')}` : '0x',
            accessList: [],
            type: 2,
            v: fields[9]?.length ? parseInt(fields[9].toString('hex'), 16) : fields[9]?.[0] || 0,
            r: fields[10]?.length ? `0x${fields[10].toString('hex')}` : '0x',
            s: fields[11]?.length ? `0x${fields[11].toString('hex')}` : '0x'
        };
        console.log(`DEBUG: txFields=`, JSON.stringify(txFields, (key, value) => typeof value === 'bigint' ? value.toString() : value));
    
        try {
            const rawTx = ethers.serializeTransaction(txFields, {
                r: txFields.r,
                s: txFields.s,
                v: txFields.v
            });
            const hash = ethers.keccak256(rawTx);
            console.log(`DEBUG: tx hash=${hash}, data=${txFields.data}`);
            return { hash, data: txFields.data };
        } catch (e) {
            console.error(`DEBUG: serialize error:`, e.message);
            return null;
        }
    }

    async processTransaction(txData) {
        const step2 = performance.now()
        let tx;
        try {
            const rlpEncodedTx = ethers.hexlify(txData);
            tx = ethers.Transaction.from(rlpEncodedTx);
            console.log("==============================================");
            if (
                this.alwaysUnwantedMethodIds.has(tx.data.slice(0, 10)) ||
                this.conditionalUnwantedMethodIds.has([tx.data.slice(0, 10), tx.to.toLowerCase()]) ||
                alwaysUnwantedToAddresses.has(tx.to.toLowerCase())
            ) {
                console.log(`DEBUG_METHOD_SKIP: Skipping tx ${tx.hash}, methodId=${tx.data.slice(0, 10)}, to=${tx.to}`);
                return;
            }
        } 
        catch (e) {
            if (e.message.includes('priorityFee cannot be more than maxFee')) {
                console.warn(`Invalid fees for txData length=${txData.length}, attempting manual parse`);
                try {
                    const decoded = ethers.decodeRlp(ethers.hexlify(txData.slice(1))); // Skip type byte
                    if (Array.isArray(decoded) && decoded.length >= 8) {
                        tx = {
                            hash: ethers.keccak256(ethers.hexlify(txData)),
                            data: decoded[7]?.startsWith('0x') ? decoded[7] : '0x',
                            to: decoded[5]?.startsWith('0x') ? decoded[5] : null,
                            from: null // Can't reliably get 'from' without signature
                        };
                        if (
                            !tx.to ||
                            this.alwaysUnwantedMethodIds.has(tx.data.slice(0, 10)) ||
                            this.conditionalUnwantedMethodIds.has([tx.data.slice(0, 10), tx.to.toLowerCase()]) ||
                            alwaysUnwantedToAddresses.has(tx.to.toLowerCase())
                        ) {
                            console.log(`DEBUG_METHOD_SKIP: Skipping invalid fee tx, methodId=${tx.data.slice(0, 10)}, to=${tx.to || 'null'}`);
                            return;
                        }
                    } else {
                        console.error(`Failed to parse RLP for txData length=${txData.length}, skipping`);
                        return;
                    }
                } catch (rlpError) {
                    console.error(`RLP parse failed: ${rlpError.message}, skipping`);
                    return;
                }
            } else {
                console.error(`Error decoding tx: ${e.message}, skipping`);
                return;
            }
        }
        // console.log(`DEBUG: txData length=${txData.length}, hex=${txData.toString('hex')}`);
        // console.log("Real method id ", tx.data.slice(0, 10));
        // console.log(`
        //     ${new Date().toISOString()} Hash ${tx.hash}
        //     tx.type ${tx.type}
        //     tx.to ${tx.to}
        //     tx.from ${tx.from}
        //     tx.nonce ${tx.nonce}
        //     tx.value ${tx.value}
        // `)
        //console.log("tx.data", tx.data);
        console.log("==============================================");

        const step3 = performance.now()

        const txToLower = tx.to.toLowerCase();

        transactionsData.push({
            hash: tx.hash,
            from: tx.from,
            to: txToLower,
            value: tx.value,
            data: tx.data
        });
        
        // console.log(`UNKNOWN ADDRESSS 🚨 🚨 🚨 🚨 🚨 🚨 on hash : ${tx.hash}
        //     tx.from : ${tx.from}
        //     tx.to : ${txToLower}
        //     tx.value : ${tx.value}
        //     tx.data : ${tx.data}
        // `)
        
    }

    printAggregatedPairs(blockNumber) {
        console.log(`Transactions Data for blockNumber ${blockNumber}:`, transactionsData);
        
        // ===== ENVOI AU SIMULATEUR RUST =====
        if (rustSocket && rustSocket.readyState === WebSocket.OPEN && transactionsData.length > 0) {
            const blockData = {
                block_number: blockNumber,
                transactions: transactionsData.map(tx => ({
                    hash: tx.hash,
                    from: tx.from,
                    to: tx.to,
                    value: tx.value?.toString() || "0",
                    data: tx.data
                }))
            };
            
            try {
                const jsonData = JSON.stringify(blockData);
                rustSocket.send(jsonData); // Use send for WebSocket
                console.log(`🚀 Envoyé ${transactionsData.length} TXs au simulateur Rust`);
            } catch (e) {
                console.error('❌ Erreur envoi vers Rust:', e.message);
            }
        } else if (transactionsData.length > 0) {
            console.log('⚠️ WebSocket Rust pas prête, simulation skippée');
        }
    }

    setupBlockListener() {
        this.wsProvider.on('block',async (blockNumber) => {
            try { 
                const currentTimeMs = performance.now();
                this.lastBlockTime = currentTimeMs;
                this.currentBlockNumber = blockNumber ;
                console.log(`Block ${blockNumber} received ${currentTimeMs- this.newFeedTime} ms after Feed`)
                console.log(`${new Date(Date.now()).toISOString()} 📦 Block ${blockNumber}`)
                const block = await this.wsProvider.getBlock(blockNumber, true);
                const txCount = block.transactions.length;
                console.log(`Block ${blockNumber} has ${txCount} transactions`);
            } catch (error) {
                console.error('Error processing block:', error);
            }
        });
    }
}

// Run the tracker
const tracker = new FeedRelayTracker();
tracker.startListening().catch(console.error);
tracker.setupBlockListener();
