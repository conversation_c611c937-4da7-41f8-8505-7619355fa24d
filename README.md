This is a simple testing repo that can faciliate you the set up a lot.
In order to run this you need to do the following :

1) In one terminal run the feed relay : https://docs.arbitrum.io/run-arbitrum-node/sequencer/run-feed-relay
docker run --rm -it  -p 0.0.0.0:9642:9642 --entrypoint relay offchainlabs/nitro-node:v3.6.5-89cef87 --node.feed.output.addr=0.0.0.0 --node.feed.input.url=wss://arb1-feed.arbitrum.io/feed --chain.id=42161

2) In another terminal, you can run the js script which is the sequencer feed decoder. It will decode each L2Msg received from the feed relay, make a basic filter of some txs and send the remaining txs to the REVM for simulation.

3) IN another terminal you can run the Rust which will receive the info from the Js script.

The Rust code is a very basic version which is way too slow and not optimized at all, but it gives you a very good idea of what needs to be done and you can work from that basis already if you wish so.
You do not need to edit anything in the JS script, it should be working as it is...the optimisation is really on the Rust side here..
It's definitely possible later to code in Rust the whole feed decoding too but that's not the goal of this test.
The goal here is really the benchmarks below which will allow us to choose the perfect candidate for that.







Benchmarks we're interested in :

After the potential initialization / warm-up of the revm is done and after simulating 1000 txs:
 
- what is the average time it takes to simulate a transaction with the top 3 fastest and top 3 slowest too, including the contract address the tx was sent to ?
- what percentage of txs was correctly simulated ? Meaning that it correctly predicts whether the tx will succeed or fail AND if it succeeds that the resulting logs are correct too. And for the incorrect simulations, the reasons.
- what is the average time to simulate all the transactions in a block (as parallelism can be used potentially) ? Also what are the top 3 fastest and top 3 slowest blocks simulated, including the amount of txs in each of them.
