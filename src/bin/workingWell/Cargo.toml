[package]
name = "revm-sim"
version = "0.1.0"
edition = "2021"

[dependencies]
revm = { version = "14.0.3", features = ["std", "ethersdb"] }
ethers = { version = "2.0", features = ["ws"] }
hex = "0.4"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio-tungstenite = "0.20"
futures-util = "0.3"
chrono = { version = "0.4", features = ["serde"] }
dashmap = "5.0"

[[bin]]
name = "model-full-rpc-perfect"
path = "src/bin/model-full-rpc-perfect.rs"
