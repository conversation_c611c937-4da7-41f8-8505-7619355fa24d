[package]
name = "revm-sim"
version = "0.1.0"
edition = "2021"

[dependencies]
revm = { version = "3.5", features = ["std", "ethersdb"] }
ethers = { version = "2.0", features = ["ws"] }
hex = "0.4"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio-tungstenite = "0.18"
futures-util = "0.3"
chrono = { version = "0.4", features = ["serde"] }
dashmap = "5.0"
lru = "0.8"
parking_lot = "0.12"
tracing = "0.1"
tracing-subscriber = "0.3"
rayon = "1.7"

[[bin]]
name = "model-full-rpc-perfect"
path = "src/bin/model-full-rpc-perfect.rs"
