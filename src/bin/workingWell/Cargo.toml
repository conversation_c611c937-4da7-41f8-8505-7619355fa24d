[package]
name = "revm-sim"
version = "0.1.0"
edition = "2021"

[dependencies]
revm = { version = "14.0", features = ["std", "ethersdb"] }
ethers = { version = "2.0", features = ["ws"] }
hex = "0.4"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio-tungstenite = "0.24"
futures-util = "0.3"
chrono = { version = "0.4", features = ["serde"] }
rayon = "1.10"
dashmap = "6.1"
parking_lot = "0.12"
crossbeam = "0.8"
once_cell = "1.20"
num_cpus = "1.16"

[[bin]]
name = "model-full-rpc-perfect"
path = "model-full-rpc-perfect.rs"

[[bin]]
name = "optimized-simulator"
path = "optimized-simulator.rs"

[[bin]]
name = "ultra-fast-simulator"
path = "ultra-fast-simulator.rs"
