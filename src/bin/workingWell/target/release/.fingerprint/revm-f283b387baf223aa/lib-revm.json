{"rustc": 11410426090777951712, "features": "[\"blst\", \"c-kzg\", \"default\", \"ethersdb\", \"portable\", \"secp256k1\", \"std\"]", "declared_features": "[\"alloydb\", \"arbitrary\", \"asm-keccak\", \"blst\", \"c-kzg\", \"default\", \"dev\", \"ethersdb\", \"hashbrown\", \"kzg-rs\", \"memory_limit\", \"negate-optimism-default-handler\", \"optimism\", \"optimism-default-handler\", \"optional_balance_check\", \"optional_beneficiary_reward\", \"optional_block_gas_limit\", \"optional_eip3607\", \"optional_gas_refund\", \"optional_no_base_fee\", \"portable\", \"secp256k1\", \"serde\", \"serde-json\", \"std\", \"test-utils\"]", "target": 3188273698379464497, "profile": 1826258101150689171, "path": 17304427451436202613, "deps": [[2828590642173593838, "cfg_if", false, 2360946632493973990], [5030409040180886167, "ethers_core", false, 16708402524625017190], [8606528971915231871, "revm_interpreter", false, 8613445295041530017], [9122563107207267705, "dyn_clone", false, 6463326242327815291], [12393800526703971956, "tokio", false, 10661118670361127934], [13389873682703403340, "revm_precompile", false, 9744172490452368589], [14417566112632113257, "ethers_providers", false, 7059316630981688757], [18125022703902813197, "auto_impl", false, 15996247202045356743]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/revm-f283b387baf223aa/dep-lib-revm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}