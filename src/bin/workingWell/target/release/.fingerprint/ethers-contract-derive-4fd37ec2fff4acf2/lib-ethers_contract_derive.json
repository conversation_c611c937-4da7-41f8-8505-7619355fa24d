{"rustc": 11410426090777951712, "features": "[\"providers\"]", "declared_features": "[\"default\", \"providers\"]", "target": 12066720079533700665, "profile": 1369601567987815722, "path": 12597907742466420137, "deps": [[2141440161325128830, "ethers_contract_abigen", false, 7235515956098786575], [3060637413840920116, "proc_macro2", false, 16194028987062731682], [4974441333307933176, "syn", false, 6166293393593605859], [5030409040180886167, "ethers_core", false, 12203459766576205678], [8569119365930580996, "serde_json", false, 13606500928634281978], [10273615881155074728, "inflector", false, 1468012626560217820], [13003293521383684806, "hex", false, 4970068397666352841], [17990358020177143287, "quote", false, 9669055028043198915]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/ethers-contract-derive-4fd37ec2fff4acf2/dep-lib-ethers_contract_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}