{"rustc": 11410426090777951712, "features": "[\"blst\", \"c-kzg\", \"portable\", \"secp256k1\", \"std\"]", "declared_features": "[\"asm-keccak\", \"blst\", \"c-kzg\", \"default\", \"hashbrown\", \"kzg-rs\", \"negate-optimism-default-handler\", \"optimism\", \"optimism-default-handler\", \"portable\", \"secp256k1\", \"secp256r1\", \"std\"]", "target": 15028308191872412523, "profile": 1826258101150689171, "path": 4599294618594523639, "deps": [[1971561991810628325, "blst", false, 4689934464839947474], [2828590642173593838, "cfg_if", false, 2360946632493973990], [3434989764622224963, "k256", false, 17952580505559851961], [3653540371698377928, "revm_primitives", false, 7253096710469081557], [3722963349756955755, "once_cell", false, 9764645754324192435], [5421974900926453538, "secp256k1", false, 14557420443487831448], [9857275760291862238, "sha2", false, 380192363599838035], [11512365307591312924, "c_kzg", false, 9819572148165368892], [15603583605579657406, "ripemd", false, 5214897891514317877], [15963096839140239429, "aurora_engine_modexp", false, 18354250777528332414], [16774410225931621389, "bn", false, 7061101734362852426]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/revm-precompile-b1a0d826390403af/dep-lib-revm_precompile", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}