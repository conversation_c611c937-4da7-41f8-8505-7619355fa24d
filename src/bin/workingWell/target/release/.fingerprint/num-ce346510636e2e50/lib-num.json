{"rustc": 11410426090777951712, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"libm\", \"num-bigint\", \"rand\", \"serde\", \"std\"]", "target": 10053607161131906775, "profile": 2040997289075261528, "path": 11584341248608943477, "deps": [[2819946551904607991, "num_rational", false, 5267764595078487074], [5157631553186200874, "num_traits", false, 15211552276111265615], [5666221976914082401, "num_iter", false, 17409824241693403722], [12319020793864570031, "num_complex", false, 3016561841470407726], [12528732512569713347, "num_bigint", false, 3907268726444699324], [16795989132585092538, "num_integer", false, 14255996414446925796]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/num-ce346510636e2e50/dep-lib-num", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}