{"rustc": 11410426090777951712, "features": "[\"raw\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2040997289075261528, "path": 6120832873142416472, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/hashbrown-471e1cc1672a9574/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}