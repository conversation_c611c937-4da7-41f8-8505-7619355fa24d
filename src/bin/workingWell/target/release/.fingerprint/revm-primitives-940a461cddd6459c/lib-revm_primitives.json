{"rustc": 11410426090777951712, "features": "[\"c-kzg\", \"portable\", \"std\"]", "declared_features": "[\"arbitrary\", \"asm-keccak\", \"c-kzg\", \"default\", \"dev\", \"hashbrown\", \"kzg-rs\", \"memory_limit\", \"negate-optimism-default-handler\", \"optimism\", \"optimism-default-handler\", \"optional_balance_check\", \"optional_beneficiary_reward\", \"optional_block_gas_limit\", \"optional_eip3607\", \"optional_gas_refund\", \"optional_no_base_fee\", \"portable\", \"rand\", \"serde\", \"std\"]", "target": 18261493983416912692, "profile": 1826258101150689171, "path": 3006954490534938863, "deps": [[2828590642173593838, "cfg_if", false, 2360946632493973990], [6186849965650146671, "alloy_primitives", false, 16243496159431598888], [7896293946984509699, "bitflags", false, 12095601044781869001], [8128813316836579245, "enumn", false, 4069231281366028919], [9122563107207267705, "dyn_clone", false, 6463326242327815291], [10197904061182704143, "alloy_eip7702", false, 8930034257879138047], [11512365307591312924, "c_kzg", false, 9819572148165368892], [13068907861756534239, "alloy_eip2930", false, 6304436378450535010], [15977791033689874879, "bitvec", false, 10470352937722241390], [18125022703902813197, "auto_impl", false, 15996247202045356743]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/revm-primitives-940a461cddd6459c/dep-lib-revm_primitives", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}