{"rustc": 11410426090777951712, "features": "[\"ethereum_kzg_settings\", \"portable\", \"std\"]", "declared_features": "[\"default\", \"ethereum_kzg_settings\", \"generate-bindings\", \"no-threads\", \"portable\", \"serde\", \"std\"]", "target": 1931979848736378922, "profile": 2040997289075261528, "path": 6000015521309565948, "deps": [[530211389790465181, "hex", false, 546361341630846378], [1971561991810628325, "blst", false, 4689934464839947474], [3722963349756955755, "once_cell", false, 9764645754324192435], [4684437522915235464, "libc", false, 10101540239463287881], [11512365307591312924, "build_script_build", false, 11910099505593703644]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/c-kzg-787eaee97ce87c6f/dep-lib-c_kzg", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}