{"rustc": 11410426090777951712, "features": "[\"abigen\", \"ethers-contract-abigen\", \"ethers-contract-derive\", \"ethers-providers\", \"providers\", \"rustls\"]", "declared_features": "[\"abigen\", \"abigen-offline\", \"abigen-online\", \"celo\", \"default\", \"eip712\", \"ethers-contract-abigen\", \"ethers-contract-derive\", \"ethers-providers\", \"legacy\", \"openssl\", \"optimism\", \"providers\", \"rustls\"]", "target": 7173054194013103692, "profile": 2040997289075261528, "path": 16580206947088314435, "deps": [[2141440161325128830, "ethers_contract_abigen", false, 14579688517304672031], [3722963349756955755, "once_cell", false, 9764645754324192435], [5030409040180886167, "ethers_core", false, 16708402524625017190], [6264115378959545688, "pin_project", false, 8868036426838728792], [8008191657135824715, "thiserror", false, 15853144003657949619], [8524202948771681628, "ethers_contract_derive", false, 4045101282407695065], [8569119365930580996, "serde_json", false, 9261027246967640101], [9689903380558560274, "serde", false, 7003183978215405354], [10629569228670356391, "futures_util", false, 5923197590174115555], [13003293521383684806, "hex", false, 6602573937307272397], [14417566112632113257, "ethers_providers", false, 7059316630981688757]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/ethers-contract-4614226ffab0d57c/dep-lib-ethers_contract", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}