{"rustc": 11410426090777951712, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 3965174974797606104, "profile": 2040997289075261528, "path": 13399844477881713723, "deps": [[99287295355353247, "data_encoding", false, 6512727051437831808], [3712811570531045576, "byteorder", false, 1065683037752891286], [4359956005902820838, "utf8", false, 4581884408541029623], [5986029879202738730, "log", false, 4518613233802127368], [6163892036024256188, "httparse", false, 6181769446186707351], [8008191657135824715, "thiserror", false, 15853144003657949619], [9010263965687315507, "http", false, 16062607443102644317], [10724389056617919257, "sha1", false, 9557437690621937958], [13208667028893622512, "rand", false, 2353899995882732375], [16066129441945555748, "bytes", false, 12580631520938625009]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tungstenite-47f3c45a30fb8b92/dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}