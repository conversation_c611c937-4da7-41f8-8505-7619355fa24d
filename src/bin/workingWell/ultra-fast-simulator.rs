use std::sync::Arc;
use std::collections::HashMap;
use std::time::{Duration, Instant};
use revm::db::{CacheDB, EthersDB};
use revm::primitives::{AccountInfo, B256, U256, Address, TransactTo, TxEnv, Env, BlockEnv, ExecutionResult};
use revm::{Database, Evm};
use ethers::providers::{Provider, Ws};
use serde::{Deserialize, Serialize};
use tokio_tungstenite::{accept_async, tungstenite::Message};
use tokio::net::TcpListener;
use futures_util::{SinkExt, StreamExt};
use std::str::FromStr;
use revm::primitives::Bytes;
use rayon::prelude::*;
use dashmap::DashMap;
use parking_lot::{RwLock, Mutex};
use crossbeam::channel::{bounded, Receiver, Sender};
use once_cell::sync::Lazy;

// ============================================================================
// ULTRA-FAST TRANSACTION STRUCTURES
// ============================================================================

#[derive(Debug, Deserialize, Clone)]
struct FeedTransaction {
    hash: String,
    from: Option<String>,
    to: String,
    value: String,
    data: String,
    gas_limit: Option<String>,
    gas_price: Option<String>,
    max_priority_fee: Option<String>,
    nonce: Option<String>,
}

#[derive(Debug, Deserialize)]
struct BlockData {
    block_number: u64,
    transactions: Vec<FeedTransaction>,
}

#[derive(Debug, Serialize, Clone)]
struct TransactionResult {
    hash: String,
    contract_address: String,
    success: bool,
    execution_time_us: f64,
    gas_used: u64,
    failure_reason: Option<String>,
}

#[derive(Debug, Serialize)]
struct BlockSimulationResult {
    block_number: u64,
    total_transactions: usize,
    successful_transactions: usize,
    total_time_ms: f64,
    average_time_us: f64,
    fastest_transactions: Vec<TransactionResult>,
    slowest_transactions: Vec<TransactionResult>,
    accuracy_percentage: f64,
    parallelism_factor: f64,
}

// ============================================================================
// OBJECT POOL FOR MEMORY OPTIMIZATION
// ============================================================================

struct ObjectPool<T> {
    pool: crossbeam::queue::SegQueue<T>,
    factory: Box<dyn Fn() -> T + Send + Sync>,
}

impl<T> ObjectPool<T> {
    fn new<F>(factory: F) -> Self 
    where 
        F: Fn() -> T + Send + Sync + 'static 
    {
        Self {
            pool: crossbeam::queue::SegQueue::new(),
            factory: Box::new(factory),
        }
    }
    
    fn get(&self) -> T {
        self.pool.pop().unwrap_or_else(|| (self.factory)())
    }
    
    fn return_object(&self, obj: T) {
        self.pool.push(obj);
    }
}

// Global object pools for maximum performance
static ENV_POOL: Lazy<ObjectPool<Env>> = Lazy::new(|| {
    ObjectPool::new(|| {
        let mut env = Env::default();
        env.cfg.chain_id = 42161;
        env
    })
});

static TXENV_POOL: Lazy<ObjectPool<TxEnv>> = Lazy::new(|| {
    ObjectPool::new(|| TxEnv::default())
});

// ============================================================================
// ULTRA-FAST BENCHMARK METRICS
// ============================================================================

#[derive(Debug)]
struct UltraFastMetrics {
    transaction_times: DashMap<String, (String, f64, bool)>, // hash -> (contract, time_us, success)
    block_times: DashMap<u64, (usize, f64)>, // block -> (tx_count, time_ms)
    successful_simulations: parking_lot::Mutex<usize>,
    total_simulations: parking_lot::Mutex<usize>,
    failure_reasons: DashMap<String, usize>,
}

impl UltraFastMetrics {
    fn new() -> Self {
        Self {
            transaction_times: DashMap::new(),
            block_times: DashMap::new(),
            successful_simulations: parking_lot::Mutex::new(0),
            total_simulations: parking_lot::Mutex::new(0),
            failure_reasons: DashMap::new(),
        }
    }
    
    fn add_transaction(&self, hash: String, contract: String, time_us: f64, success: bool, failure_reason: Option<String>) {
        self.transaction_times.insert(hash, (contract, time_us, success));
        
        let mut total = self.total_simulations.lock();
        *total += 1;
        
        if success {
            let mut successful = self.successful_simulations.lock();
            *successful += 1;
        } else if let Some(reason) = failure_reason {
            *self.failure_reasons.entry(reason).or_insert(0) += 1;
        }
    }
    
    fn add_block(&self, block_number: u64, tx_count: usize, time_ms: f64) {
        self.block_times.insert(block_number, (tx_count, time_ms));
    }
    
    fn get_fastest_transactions(&self, count: usize) -> Vec<TransactionResult> {
        let mut transactions: Vec<_> = self.transaction_times
            .iter()
            .map(|entry| {
                let (hash, (contract, time, success)) = (entry.key(), entry.value());
                TransactionResult {
                    hash: hash.clone(),
                    contract_address: contract.clone(),
                    success: *success,
                    execution_time_us: *time,
                    gas_used: 0,
                    failure_reason: None,
                }
            })
            .collect();
        
        transactions.sort_by(|a, b| a.execution_time_us.partial_cmp(&b.execution_time_us).unwrap());
        transactions.into_iter().take(count).collect()
    }
    
    fn get_slowest_transactions(&self, count: usize) -> Vec<TransactionResult> {
        let mut transactions: Vec<_> = self.transaction_times
            .iter()
            .map(|entry| {
                let (hash, (contract, time, success)) = (entry.key(), entry.value());
                TransactionResult {
                    hash: hash.clone(),
                    contract_address: contract.clone(),
                    success: *success,
                    execution_time_us: *time,
                    gas_used: 0,
                    failure_reason: None,
                }
            })
            .collect();
        
        transactions.sort_by(|a, b| b.execution_time_us.partial_cmp(&a.execution_time_us).unwrap());
        transactions.into_iter().take(count).collect()
    }
    
    fn get_fastest_blocks(&self, count: usize) -> Vec<(u64, usize, f64)> {
        let mut blocks: Vec<_> = self.block_times
            .iter()
            .map(|entry| (*entry.key(), entry.value().0, entry.value().1))
            .collect();
        
        blocks.sort_by(|a, b| a.2.partial_cmp(&b.2).unwrap());
        blocks.into_iter().take(count).collect()
    }
    
    fn get_slowest_blocks(&self, count: usize) -> Vec<(u64, usize, f64)> {
        let mut blocks: Vec<_> = self.block_times
            .iter()
            .map(|entry| (*entry.key(), entry.value().0, entry.value().1))
            .collect();
        
        blocks.sort_by(|a, b| b.2.partial_cmp(&a.2).unwrap());
        blocks.into_iter().take(count).collect()
    }
    
    fn get_total_simulations(&self) -> usize {
        *self.total_simulations.lock()
    }
    
    fn get_successful_simulations(&self) -> usize {
        *self.successful_simulations.lock()
    }
    
    fn generate_benchmark_report(&self) -> String {
        let total = self.get_total_simulations();
        if total < 1000 {
            return format!("⚠️ Need at least 1000 transactions for benchmark report. Current: {}", total);
        }
        
        let successful = self.get_successful_simulations();
        let accuracy = (successful as f64 / total as f64) * 100.0;
        
        let avg_time = if !self.transaction_times.is_empty() {
            self.transaction_times.iter().map(|entry| entry.value().1).sum::<f64>() / self.transaction_times.len() as f64
        } else {
            0.0
        };
        
        let fastest_txs = self.get_fastest_transactions(3);
        let slowest_txs = self.get_slowest_transactions(3);
        let fastest_blocks = self.get_fastest_blocks(3);
        let slowest_blocks = self.get_slowest_blocks(3);
        
        format!(
            "🎯 ULTRA-FAST BENCHMARK REPORT (After {} transactions)\n\
            ═══════════════════════════════════════════════════════════════\n\
            📊 TRANSACTION METRICS:\n\
            • Average simulation time: {:.1}µs\n\
            • Success rate: {:.2}% ({}/{})\n\
            \n\
            🚀 TOP 3 FASTEST TRANSACTIONS:\n{}\n\
            🐌 TOP 3 SLOWEST TRANSACTIONS:\n{}\n\
            \n\
            📦 TOP 3 FASTEST BLOCKS:\n{}\n\
            🐌 TOP 3 SLOWEST BLOCKS:\n{}\n\
            \n\
            ❌ FAILURE REASONS:\n{}\n\
            ═══════════════════════════════════════════════════════════════",
            total,
            avg_time,
            accuracy,
            successful,
            total,
            fastest_txs.iter().enumerate().map(|(i, tx)| 
                format!("   {}. {} → {} ({:.1}µs)", i+1, &tx.hash[..10], &tx.contract_address[..10], tx.execution_time_us)
            ).collect::<Vec<_>>().join("\n"),
            slowest_txs.iter().enumerate().map(|(i, tx)| 
                format!("   {}. {} → {} ({:.1}µs)", i+1, &tx.hash[..10], &tx.contract_address[..10], tx.execution_time_us)
            ).collect::<Vec<_>>().join("\n"),
            fastest_blocks.iter().enumerate().map(|(i, (block, tx_count, time))| 
                format!("   {}. Block {} ({} txs): {:.2}ms", i+1, block, tx_count, time)
            ).collect::<Vec<_>>().join("\n"),
            slowest_blocks.iter().enumerate().map(|(i, (block, tx_count, time))| 
                format!("   {}. Block {} ({} txs): {:.2}ms", i+1, block, tx_count, time)
            ).collect::<Vec<_>>().join("\n"),
            self.failure_reasons.iter().map(|entry| 
                format!("   • {}: {} occurrences", entry.key(), entry.value())
            ).collect::<Vec<_>>().join("\n")
        )
    }
}

// ============================================================================
// ULTRA-FAST ARBITRUM SIMULATOR WITH PARALLELIZATION
// ============================================================================

struct UltraFastArbitrumSimulator {
    provider: Arc<Provider<Ws>>,
    persistent_db: Arc<RwLock<CacheDB<EthersDB<Provider<Ws>>>>>,
    account_cache: Arc<DashMap<Address, AccountInfo>>,
    metrics: Arc<UltraFastMetrics>,
    warmup_complete: Arc<parking_lot::Mutex<bool>>,
    thread_pool: rayon::ThreadPool,
}

impl UltraFastArbitrumSimulator {
    pub async fn new(rpc_url: &str) -> Result<Self, Box<dyn std::error::Error>> {
        println!("🚀 Connecting to Arbitrum node: {}", rpc_url);
        let provider = Provider::<Ws>::connect(rpc_url).await?;
        let provider = Arc::new(provider.clone());

        // Create persistent database
        let ethers_db = EthersDB::new(provider.clone(), None)
            .ok_or("Failed to create EthersDB")?;
        let cache_db = CacheDB::new(ethers_db);
        let persistent_db = Arc::new(RwLock::new(cache_db));

        // Initialize concurrent data structures
        let account_cache = Arc::new(DashMap::new());
        let metrics = Arc::new(UltraFastMetrics::new());
        let warmup_complete = Arc::new(parking_lot::Mutex::new(false));

        // Create optimized thread pool for parallel processing
        let thread_pool = rayon::ThreadPoolBuilder::new()
            .num_threads(num_cpus::get().max(8)) // Use all available cores, minimum 8
            .thread_name(|i| format!("revm-worker-{}", i))
            .build()?;

        println!("✅ Ultra-fast simulator initialized with {} threads", thread_pool.current_num_threads());

        Ok(Self {
            provider,
            persistent_db,
            account_cache,
            metrics,
            warmup_complete,
            thread_pool,
        })
    }

    pub async fn warmup(&self) -> Result<(), Box<dyn std::error::Error>> {
        println!("🔥 Starting ultra-fast warmup process...");
        let start = Instant::now();

        // Pre-load commonly used Arbitrum contracts in parallel
        let common_addresses = vec![
            "******************************************", // WETH
            "******************************************", // USDC
            "******************************************", // ARB
            "******************************************", // USDC.e
            "******************************************", // WBTC
            "******************************************", // USDT
            "******************************************", // FRAX
            "******************************************", // DAI
        ];

        // Parallel account loading
        let futures: Vec<_> = common_addresses.into_iter().map(|addr_str| {
            let db = self.persistent_db.clone();
            let cache = self.account_cache.clone();

            async move {
                if let Ok(addr) = Address::from_str(addr_str) {
                    let account = {
                        let mut db_write = db.write();
                        db_write.basic(addr).ok().flatten()
                    };

                    if let Some(account_info) = account {
                        cache.insert(addr, account_info);
                        println!("Pre-loaded account: {}", addr_str);
                    }
                }
            }
        }).collect();

        // Execute all warmup tasks concurrently
        futures_util::future::join_all(futures).await;

        *self.warmup_complete.lock() = true;
        let warmup_time = start.elapsed();
        println!("✅ Ultra-fast warmup completed in {:.2}ms", warmup_time.as_millis());

        Ok(())
    }

    pub async fn simulate_block(
        &self,
        block_data: BlockData,
    ) -> Result<BlockSimulationResult, Box<dyn std::error::Error>> {
        let block_number = block_data.block_number;

        // Ensure warmup is complete
        if !*self.warmup_complete.lock() {
            self.warmup().await?;
        }

        println!("⚡ Ultra-fast processing Block {} with {} transactions",
            block_number, block_data.transactions.len());

        let simulation_start = Instant::now();

        // PARALLEL TRANSACTION PROCESSING - This is the key optimization!
        let results: Vec<TransactionResult> = self.thread_pool.install(|| {
            block_data.transactions
                .par_iter() // Parallel iterator - processes transactions concurrently
                .map(|tx| {
                    let tx_start = Instant::now();
                    let (success, failure_reason, gas_used) = self.simulate_single_transaction_parallel(tx, block_number);
                    let tx_time = tx_start.elapsed();
                    let tx_time_us = tx_time.as_nanos() as f64 / 1000.0;

                    // Record metrics concurrently
                    self.metrics.add_transaction(
                        tx.hash.clone(),
                        tx.to.clone(),
                        tx_time_us,
                        success,
                        failure_reason.clone()
                    );

                    TransactionResult {
                        hash: tx.hash.clone(),
                        contract_address: tx.to.clone(),
                        success,
                        execution_time_us: tx_time_us,
                        gas_used,
                        failure_reason,
                    }
                })
                .collect()
        });

        let simulation_time = simulation_start.elapsed();
        let total_time_ms = simulation_time.as_millis() as f64;
        let successful_transactions = results.iter().filter(|r| r.success).count();

        // Calculate parallelism efficiency
        let sequential_time_estimate = results.iter().map(|r| r.execution_time_us).sum::<f64>() / 1000.0;
        let parallelism_factor = sequential_time_estimate / total_time_ms;

        // Record block metrics
        self.metrics.add_block(block_number, block_data.transactions.len(), total_time_ms);

        // Get fastest and slowest transactions for this block
        let mut sorted_results = results.clone();
        sorted_results.sort_by(|a, b| a.execution_time_us.partial_cmp(&b.execution_time_us).unwrap());

        let fastest_transactions = sorted_results.iter().take(3).cloned().collect();
        let slowest_transactions = sorted_results.iter().rev().take(3).cloned().collect();

        let accuracy_percentage = if block_data.transactions.len() > 0 {
            (successful_transactions as f64 / block_data.transactions.len() as f64) * 100.0
        } else {
            0.0
        };

        let result = BlockSimulationResult {
            block_number,
            total_transactions: block_data.transactions.len(),
            successful_transactions,
            total_time_ms,
            average_time_us: if block_data.transactions.len() > 0 {
                (simulation_time.as_nanos() as f64 / 1000.0) / block_data.transactions.len() as f64
            } else {
                0.0
            },
            fastest_transactions,
            slowest_transactions,
            accuracy_percentage,
            parallelism_factor,
        };

        println!("✅ Block {} completed: {:.2}ms, {:.1}µs/TX, {:.1}% success, {:.1}x parallel speedup",
            block_number, result.total_time_ms, result.average_time_us, accuracy_percentage, parallelism_factor);

        Ok(result)
    }

    fn simulate_single_transaction_parallel(
        &self,
        tx: &FeedTransaction,
        block_number: u64,
    ) -> (bool, Option<String>, u64) {
        // Parse addresses with optimized error handling
        let to_address = match Address::from_str(&tx.to) {
            Ok(addr) => addr,
            Err(_) => return (false, Some("Invalid to address".to_string()), 0),
        };

        let caller_address = match &tx.from {
            Some(from) => match Address::from_str(from) {
                Ok(addr) => addr,
                Err(_) => return (false, Some("Invalid from address".to_string()), 0),
            },
            None => return (false, Some("Missing from address".to_string()), 0),
        };

        // Parse transaction data with memory optimization
        let calldata_bytes = if tx.data.starts_with("0x") && tx.data.len() > 2 {
            match hex::decode(&tx.data[2..]) {
                Ok(bytes) => Bytes::from(bytes),
                Err(_) => return (false, Some("Invalid hex data".to_string()), 0),
            }
        } else {
            Bytes::new()
        };

        let value = if tx.value == "0" || tx.value.is_empty() {
            U256::ZERO
        } else {
            match U256::from_str(&tx.value) {
                Ok(val) => val,
                Err(_) => return (false, Some("Invalid value".to_string()), 0),
            }
        };

        // Parse gas parameters with optimized defaults
        let gas_limit = tx.gas_limit.as_ref()
            .and_then(|g| g.parse::<u64>().ok())
            .unwrap_or(800_000);

        let gas_price = tx.gas_price.as_ref()
            .and_then(|g| U256::from_str(g).ok())
            .unwrap_or(U256::from(1_000_000_000u64));

        let gas_priority_fee = tx.max_priority_fee.as_ref()
            .and_then(|g| U256::from_str(g).ok());

        let tx_nonce = tx.nonce.as_ref()
            .and_then(|n| n.parse::<u64>().ok());

        // Ultra-fast account state management with concurrent caching
        if let Some(tx_nonce) = tx_nonce {
            // Check concurrent cache first
            let mut account_info = if let Some(cached_account) = self.account_cache.get(&caller_address) {
                cached_account.clone()
            } else {
                // Fallback to database with write lock for basic() call
                let account = {
                    let mut db = self.persistent_db.write();
                    match db.basic(caller_address) {
                        Ok(Some(account)) => account,
                        Ok(None) => {
                            // Create optimized default account
                            let default_account = AccountInfo {
                                balance: U256::from(10_000_000_000_000_000_000u64), // 10 ETH default
                                nonce: tx_nonce,
                                code_hash: B256::ZERO,
                                code: None,
                            };
                            // Insert the default account immediately
                            db.insert_account_info(caller_address, default_account.clone());
                            default_account
                        },
                        Err(_) => return (false, Some("Failed to load account".to_string()), 0),
                    }
                };

                // Cache for future use
                self.account_cache.insert(caller_address, account.clone());
                account
            };

            // Adjust account state if needed
            if account_info.nonce != tx_nonce {
                account_info.nonce = tx_nonce;

                let max_fee = U256::from(gas_limit) * gas_price;
                if account_info.balance < max_fee {
                    account_info.balance = max_fee * U256::from(3); // 3x buffer for safety
                }

                // Update both cache and database
                self.account_cache.insert(caller_address, account_info.clone());

                // Update database with write lock (minimal lock time)
                {
                    let mut db = self.persistent_db.write();
                    db.insert_account_info(caller_address, account_info);
                }
            }
        }

        // Use object pools for memory optimization
        let mut env = ENV_POOL.get();
        let mut tx_env = TXENV_POOL.get();

        // Configure transaction environment
        tx_env.caller = caller_address;
        tx_env.gas_limit = gas_limit;
        tx_env.gas_price = gas_price;
        tx_env.transact_to = TransactTo::Call(to_address);
        tx_env.value = value;
        tx_env.data = calldata_bytes;
        tx_env.nonce = tx_nonce;
        tx_env.chain_id = Some(42161);
        tx_env.access_list = vec![];
        tx_env.gas_priority_fee = gas_priority_fee;
        tx_env.blob_hashes = vec![];
        tx_env.max_fee_per_blob_gas = None;
        tx_env.authorization_list = None;

        env.tx = tx_env.clone();
        env.block = BlockEnv {
            number: U256::from(block_number),
            timestamp: U256::from(chrono::Utc::now().timestamp() as u64),
            gas_limit: U256::MAX,
            difficulty: U256::ZERO,
            prevrandao: Some(B256::ZERO),
            basefee: U256::from(10_000_000u64),
            coinbase: Address::ZERO,
            blob_excess_gas_and_price: Some(revm::primitives::BlobExcessGasAndPrice::new(0)),
        };

        // Execute transaction with optimized database access
        let execution_result = {
            let mut db = self.persistent_db.write();
            let mut evm = Evm::builder()
                .with_db(&mut *db)
                .with_env(Box::new(env.clone()))
                .build();

            evm.transact()
        };

        // Return objects to pool for reuse
        ENV_POOL.return_object(env);
        TXENV_POOL.return_object(tx_env);

        // Process execution result
        match execution_result {
            Ok(res) => {
                match &res.result {
                    ExecutionResult::Success { gas_used, .. } => {
                        (true, None, *gas_used)
                    }
                    ExecutionResult::Revert { gas_used, output } => {
                        let revert_reason = if output.len() >= 4 {
                            if let Ok(decoded) = String::from_utf8(output[4..].to_vec()) {
                                format!("Revert: {}", decoded.trim_end_matches('\0'))
                            } else {
                                "Revert: Unknown".to_string()
                            }
                        } else {
                            "Revert: Empty".to_string()
                        };
                        (false, Some(revert_reason), *gas_used)
                    }
                    ExecutionResult::Halt { gas_used, reason } => {
                        let halt_reason = format!("Halt: {:?}", reason);
                        (false, Some(halt_reason), *gas_used)
                    }
                }
            }
            Err(e) => {
                let error_msg = format!("Execution error: {:?}", e);
                (false, Some(error_msg), 0)
            }
        }
    }

    pub fn get_benchmark_report(&self) -> String {
        self.metrics.generate_benchmark_report()
    }
}

// ============================================================================
// MAIN FUNCTION - ULTRA-FAST SIMULATOR
// ============================================================================

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀🚀🚀 === ULTRA-FAST ARBITRUM SIMULATOR === 🚀🚀🚀");
    println!("⚡ Mode: Parallel processing with memory optimization");
    println!("🎯 Target: Maximum speed with accuracy validation");

    // Create ultra-fast simulator
    let simulator = Arc::new(
        UltraFastArbitrumSimulator::new("wss://lingering-flashy-lambo.arbitrum-mainnet.quiknode.pro/3b4184e83918c9bb09a12af99065f77188c85fd6/").await?
    );

    // Start WebSocket server
    let listener = TcpListener::bind("127.0.0.1:9999").await?;
    println!("🌍 Ultra-fast server started on 127.0.0.1:9999");
    println!("📡 Ready for high-speed transaction processing");

    while let Ok((stream, addr)) = listener.accept().await {
        println!("🔌 New connection: {}", addr);

        let ws_stream = accept_async(stream).await?;
        let (mut ws_sender, mut ws_receiver) = ws_stream.split();


        // Handle each connection in the main loop (simpler approach)
        while let Some(msg) = ws_receiver.next().await {
            match msg? {
                Message::Text(text) => {
                    if let Ok(block_data) = serde_json::from_str::<BlockData>(&text) {
                        match simulator.simulate_block(block_data).await {
                            Ok(result) => {
                                let response = serde_json::to_string(&result)?;
                                ws_sender.send(Message::Text(response)).await?;

                                // Generate detailed benchmark report after 1000+ transactions
                                let total_sims = simulator.metrics.get_total_simulations();
                                if total_sims >= 1000 && total_sims % 1000 == 0 {
                                    let report = simulator.get_benchmark_report();
                                    println!("\n{}", report);
                                } else if total_sims > 0 && total_sims % 100 == 0 {
                                    let successful = simulator.metrics.get_successful_simulations();
                                    println!("📊 Ultra-fast Progress - Total TXs: {}, Success Rate: {:.1}%, Parallel Speedup: {:.1}x",
                                        total_sims,
                                        (successful as f64 / total_sims as f64) * 100.0,
                                        result.parallelism_factor
                                    );
                                }
                            }
                            Err(e) => {
                                println!("❌ Ultra-fast simulation error: {}", e);
                            }
                        }
                    }
                }
                Message::Close(_) => break,
                _ => {}
            }
        }
    }

    Ok(())
}
