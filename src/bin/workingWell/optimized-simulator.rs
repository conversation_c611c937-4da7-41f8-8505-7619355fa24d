use std::sync::Arc;
use std::collections::HashMap;
use std::time::{Duration, Instant};
use revm::db::{CacheDB, EthersDB};
use revm::primitives::{AccountInfo, B256, U256, Address, TransactTo, TxEnv, Env, BlockEnv, ExecutionResult};
use revm::{Database, Evm};
use ethers::providers::{Provider, Ws};
use serde::{Deserialize, Serialize};
use tokio_tungstenite::{accept_async, tungstenite::Message};
use tokio::net::TcpListener;
use futures_util::{SinkExt, StreamExt};
use std::str::FromStr;
use revm::primitives::Bytes;

// ============================================================================
// TRANSACTION AND RESULT STRUCTURES
// ============================================================================

#[derive(Debug, Deserialize)]
struct FeedTransaction {
    hash: String,
    from: Option<String>,
    to: String,
    value: String,
    data: String,
    gas_limit: Option<String>,
    gas_price: Option<String>,
    max_priority_fee: Option<String>,
    nonce: Option<String>,
}

#[derive(Debug, Deserialize)]
struct BlockData {
    block_number: u64,
    transactions: Vec<FeedTransaction>,
}

#[derive(Debug, Serialize, Clone)]
struct TransactionResult {
    hash: String,
    contract_address: String,
    success: bool,
    execution_time_us: f64,
    gas_used: u64,
    failure_reason: Option<String>,
}

#[derive(Debug, Serialize)]
struct BlockSimulationResult {
    block_number: u64,
    total_transactions: usize,
    successful_transactions: usize,
    total_time_ms: f64,
    average_time_us: f64,
    fastest_transactions: Vec<TransactionResult>,
    slowest_transactions: Vec<TransactionResult>,
    accuracy_percentage: f64,
}

// ============================================================================
// BENCHMARK METRICS SYSTEM
// ============================================================================

#[derive(Debug, Clone)]
struct BenchmarkMetrics {
    transaction_times: Vec<(String, String, f64)>, // (hash, contract, time_us)
    block_times: Vec<(u64, usize, f64)>, // (block_number, tx_count, time_ms)
    successful_simulations: usize,
    total_simulations: usize,
    failure_reasons: HashMap<String, usize>,
}

impl BenchmarkMetrics {
    fn new() -> Self {
        Self {
            transaction_times: Vec::new(),
            block_times: Vec::new(),
            successful_simulations: 0,
            total_simulations: 0,
            failure_reasons: HashMap::new(),
        }
    }
    
    fn add_transaction(&mut self, hash: String, contract: String, time_us: f64, success: bool, failure_reason: Option<String>) {
        self.transaction_times.push((hash, contract, time_us));
        self.total_simulations += 1;
        if success {
            self.successful_simulations += 1;
        } else if let Some(reason) = failure_reason {
            *self.failure_reasons.entry(reason).or_insert(0) += 1;
        }
    }
    
    fn add_block(&mut self, block_number: u64, tx_count: usize, time_ms: f64) {
        self.block_times.push((block_number, tx_count, time_ms));
    }
    
    fn get_fastest_transactions(&self, count: usize) -> Vec<TransactionResult> {
        let mut sorted = self.transaction_times.clone();
        sorted.sort_by(|a, b| a.2.partial_cmp(&b.2).unwrap());
        sorted.into_iter().take(count).map(|(hash, contract, time)| {
            TransactionResult {
                hash,
                contract_address: contract,
                success: true,
                execution_time_us: time,
                gas_used: 0,
                failure_reason: None,
            }
        }).collect()
    }
    
    fn get_slowest_transactions(&self, count: usize) -> Vec<TransactionResult> {
        let mut sorted = self.transaction_times.clone();
        sorted.sort_by(|a, b| b.2.partial_cmp(&a.2).unwrap());
        sorted.into_iter().take(count).map(|(hash, contract, time)| {
            TransactionResult {
                hash,
                contract_address: contract,
                success: true,
                execution_time_us: time,
                gas_used: 0,
                failure_reason: None,
            }
        }).collect()
    }
    
    fn get_fastest_blocks(&self, count: usize) -> Vec<(u64, usize, f64)> {
        let mut sorted = self.block_times.clone();
        sorted.sort_by(|a, b| a.2.partial_cmp(&b.2).unwrap());
        sorted.into_iter().take(count).collect()
    }
    
    fn get_slowest_blocks(&self, count: usize) -> Vec<(u64, usize, f64)> {
        let mut sorted = self.block_times.clone();
        sorted.sort_by(|a, b| b.2.partial_cmp(&a.2).unwrap());
        sorted.into_iter().take(count).collect()
    }
    
    fn generate_benchmark_report(&self) -> String {
        if self.total_simulations < 1000 {
            return format!("⚠️ Need at least 1000 transactions for benchmark report. Current: {}", self.total_simulations);
        }
        
        let accuracy = (self.successful_simulations as f64 / self.total_simulations as f64) * 100.0;
        let avg_time = if !self.transaction_times.is_empty() {
            self.transaction_times.iter().map(|(_, _, time)| time).sum::<f64>() / self.transaction_times.len() as f64
        } else {
            0.0
        };
        
        let fastest_txs = self.get_fastest_transactions(3);
        let slowest_txs = self.get_slowest_transactions(3);
        let fastest_blocks = self.get_fastest_blocks(3);
        let slowest_blocks = self.get_slowest_blocks(3);
        
        format!(
            "🎯 BENCHMARK REPORT (After {} transactions)\n\
            ═══════════════════════════════════════════════════════════════\n\
            📊 TRANSACTION METRICS:\n\
            • Average simulation time: {:.1}µs\n\
            • Success rate: {:.2}% ({}/{})\n\
            \n\
            🚀 TOP 3 FASTEST TRANSACTIONS:\n{}\n\
            🐌 TOP 3 SLOWEST TRANSACTIONS:\n{}\n\
            \n\
            📦 TOP 3 FASTEST BLOCKS:\n{}\n\
            🐌 TOP 3 SLOWEST BLOCKS:\n{}\n\
            \n\
            ❌ FAILURE REASONS:\n{}\n\
            ═══════════════════════════════════════════════════════════════",
            self.total_simulations,
            avg_time,
            accuracy,
            self.successful_simulations,
            self.total_simulations,
            fastest_txs.iter().enumerate().map(|(i, tx)| 
                format!("   {}. {} → {} ({:.1}µs)", i+1, &tx.hash[..10], &tx.contract_address[..10], tx.execution_time_us)
            ).collect::<Vec<_>>().join("\n"),
            slowest_txs.iter().enumerate().map(|(i, tx)| 
                format!("   {}. {} → {} ({:.1}µs)", i+1, &tx.hash[..10], &tx.contract_address[..10], tx.execution_time_us)
            ).collect::<Vec<_>>().join("\n"),
            fastest_blocks.iter().enumerate().map(|(i, (block, tx_count, time))| 
                format!("   {}. Block {} ({} txs): {:.2}ms", i+1, block, tx_count, time)
            ).collect::<Vec<_>>().join("\n"),
            slowest_blocks.iter().enumerate().map(|(i, (block, tx_count, time))| 
                format!("   {}. Block {} ({} txs): {:.2}ms", i+1, block, tx_count, time)
            ).collect::<Vec<_>>().join("\n"),
            self.failure_reasons.iter().map(|(reason, count)| 
                format!("   • {}: {} occurrences", reason, count)
            ).collect::<Vec<_>>().join("\n")
        )
    }
}

// ============================================================================
// OPTIMIZED ARBITRUM SIMULATOR
// ============================================================================

struct OptimizedArbitrumSimulator {
    provider: Arc<Provider<Ws>>,
    persistent_db: Option<CacheDB<EthersDB<Provider<Ws>>>>,
    account_cache: HashMap<Address, AccountInfo>,
    metrics: BenchmarkMetrics,
    warmup_complete: bool,
}

impl OptimizedArbitrumSimulator {
    pub async fn new(rpc_url: &str) -> Result<Self, Box<dyn std::error::Error>> {
        println!("🔗 Connecting to Arbitrum node: {}", rpc_url);
        let provider = Provider::<Ws>::connect(rpc_url).await?;
        let provider = Arc::new(provider);
        
        println!("✅ Connection successful - Optimized mode with persistent state");
        
        Ok(Self {
            provider,
            persistent_db: None,
            account_cache: HashMap::new(),
            metrics: BenchmarkMetrics::new(),
            warmup_complete: false,
        })
    }
    
    pub async fn warmup(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("🔥 Starting warmup process...");
        let start = Instant::now();
        
        // Create persistent database
        let ethers_db = EthersDB::new(self.provider.clone(), None)
            .ok_or("Failed to create EthersDB")?;
        let mut cache_db = CacheDB::new(ethers_db);
        
        // Pre-load commonly used accounts and contracts
        let common_addresses = vec![
            "******************************************", // WETH
            "******************************************", // USDC
            "******************************************", // ARB
        ];
        
        for addr_str in common_addresses {
            if let Ok(addr) = Address::from_str(addr_str) {
                if let Ok(Some(account)) = cache_db.basic(addr) {
                    self.account_cache.insert(addr, account);
                    println!("Pre-loaded account: {}", addr_str);
                }
            }
        }
        
        self.persistent_db = Some(cache_db);
        self.warmup_complete = true;
        let warmup_time = start.elapsed();
        println!("✅ Warmup completed in {:.2}ms", warmup_time.as_millis());
        
        Ok(())
    }

    pub async fn simulate_block(
        &mut self,
        block_data: BlockData,
    ) -> Result<BlockSimulationResult, Box<dyn std::error::Error>> {
        let block_number = block_data.block_number;

        if !self.warmup_complete {
            self.warmup().await?;
        }

        println!("📦 Processing Block {} with {} transactions",
            block_number, block_data.transactions.len());

        let simulation_start = Instant::now();
        let mut successful_transactions = 0;
        let mut transaction_results = Vec::new();

        // Process transactions sequentially for now
        for tx in &block_data.transactions {
            let tx_start = Instant::now();
            let (success, failure_reason) = self.simulate_single_transaction(tx, block_number).await;
            let tx_time = tx_start.elapsed();
            let tx_time_us = tx_time.as_nanos() as f64 / 1000.0;

            if success {
                successful_transactions += 1;
            }

            // Record metrics
            self.metrics.add_transaction(
                tx.hash.clone(),
                tx.to.clone(),
                tx_time_us,
                success,
                failure_reason.clone()
            );

            transaction_results.push(TransactionResult {
                hash: tx.hash.clone(),
                contract_address: tx.to.clone(),
                success,
                execution_time_us: tx_time_us,
                gas_used: 0,
                failure_reason,
            });
        }

        let simulation_time = simulation_start.elapsed();
        let total_time_ms = simulation_time.as_millis() as f64;

        // Record block metrics
        self.metrics.add_block(block_number, block_data.transactions.len(), total_time_ms);

        // Get fastest and slowest transactions for this block
        let mut sorted_results = transaction_results.clone();
        sorted_results.sort_by(|a, b| a.execution_time_us.partial_cmp(&b.execution_time_us).unwrap());

        let fastest_transactions = sorted_results.iter().take(3).cloned().collect();
        let slowest_transactions = sorted_results.iter().rev().take(3).cloned().collect();

        let accuracy_percentage = if block_data.transactions.len() > 0 {
            (successful_transactions as f64 / block_data.transactions.len() as f64) * 100.0
        } else {
            0.0
        };

        let result = BlockSimulationResult {
            block_number,
            total_transactions: block_data.transactions.len(),
            successful_transactions,
            total_time_ms,
            average_time_us: if block_data.transactions.len() > 0 {
                (simulation_time.as_nanos() as f64 / 1000.0) / block_data.transactions.len() as f64
            } else {
                0.0
            },
            fastest_transactions,
            slowest_transactions,
            accuracy_percentage,
        };

        println!("✅ Block {} completed: {:.2}ms, {:.1}µs/TX, {:.1}% success",
            block_number, result.total_time_ms, result.average_time_us, accuracy_percentage);

        Ok(result)
    }

    async fn simulate_single_transaction(
        &mut self,
        tx: &FeedTransaction,
        block_number: u64,
    ) -> (bool, Option<String>) {
        // Parse addresses
        let to_address = match Address::from_str(&tx.to) {
            Ok(addr) => addr,
            Err(_) => return (false, Some("Invalid to address".to_string())),
        };

        let caller_address = match &tx.from {
            Some(from) => match Address::from_str(from) {
                Ok(addr) => addr,
                Err(_) => return (false, Some("Invalid from address".to_string())),
            },
            None => return (false, Some("Missing from address".to_string())),
        };

        // Parse transaction data
        let calldata_bytes = if tx.data.starts_with("0x") && tx.data.len() > 2 {
            match hex::decode(&tx.data[2..]) {
                Ok(bytes) => Bytes::from(bytes),
                Err(_) => return (false, Some("Invalid hex data".to_string())),
            }
        } else {
            Bytes::new()
        };

        let value = if tx.value == "0" || tx.value.is_empty() {
            U256::ZERO
        } else {
            match U256::from_str(&tx.value) {
                Ok(val) => val,
                Err(_) => return (false, Some("Invalid value".to_string())),
            }
        };

        // Parse gas parameters with defaults
        let gas_limit = tx.gas_limit.as_ref()
            .and_then(|g| g.parse::<u64>().ok())
            .unwrap_or(800_000);

        let gas_price = tx.gas_price.as_ref()
            .and_then(|g| U256::from_str(g).ok())
            .unwrap_or(U256::from(1_000_000_000u64));

        let gas_priority_fee = tx.max_priority_fee.as_ref()
            .and_then(|g| U256::from_str(g).ok());

        let tx_nonce = tx.nonce.as_ref()
            .and_then(|n| n.parse::<u64>().ok());

        // Optimized account state management with caching
        if let Some(tx_nonce) = tx_nonce {
            if let Some(db) = &mut self.persistent_db {
                let mut account_info = if let Some(cached_account) = self.account_cache.get(&caller_address) {
                    cached_account.clone()
                } else {
                    match db.basic(caller_address) {
                        Ok(Some(account)) => {
                            self.account_cache.insert(caller_address, account.clone());
                            account
                        },
                        Ok(None) => {
                            let default_account = AccountInfo {
                                balance: U256::from(1_000_000_000_000_000_000u64),
                                nonce: tx_nonce,
                                code_hash: B256::ZERO,
                                code: None,
                            };
                            db.insert_account_info(caller_address, default_account.clone());
                            self.account_cache.insert(caller_address, default_account.clone());
                            default_account
                        },
                        Err(_) => return (false, Some("Failed to load account".to_string())),
                    }
                };

                // Adjust account state if needed
                if account_info.nonce != tx_nonce {
                    account_info.nonce = tx_nonce;

                    let max_fee = U256::from(gas_limit) * gas_price;
                    if account_info.balance < max_fee {
                        account_info.balance = max_fee * U256::from(2);
                    }

                    db.insert_account_info(caller_address, account_info.clone());
                    self.account_cache.insert(caller_address, account_info);
                }
            }
        }

        // Configure EVM environment
        let mut env = Env::default();
        env.cfg.chain_id = 42161;

        env.tx = TxEnv {
            caller: caller_address,
            gas_limit,
            gas_price,
            transact_to: TransactTo::Call(to_address),
            value,
            data: calldata_bytes,
            nonce: tx_nonce,
            chain_id: Some(42161),
            access_list: vec![],
            gas_priority_fee,
            blob_hashes: vec![],
            max_fee_per_blob_gas: None,
            authorization_list: None,
        };

        env.block = BlockEnv {
            number: U256::from(block_number),
            timestamp: U256::from(chrono::Utc::now().timestamp() as u64),
            gas_limit: U256::MAX,
            difficulty: U256::ZERO,
            prevrandao: Some(B256::ZERO),
            basefee: U256::from(10_000_000u64),
            coinbase: Address::ZERO,
            blob_excess_gas_and_price: Some(revm::primitives::BlobExcessGasAndPrice::new(0)),
        };

        // Execute transaction with persistent database
        if let Some(db) = &mut self.persistent_db {
            let mut evm = Evm::builder()
                .with_db(db)
                .with_env(Box::new(env))
                .build();

            match evm.transact() {
                Ok(res) => {
                    match &res.result {
                        ExecutionResult::Success { gas_used, .. } => {
                            (true, None)
                        }
                        ExecutionResult::Revert { gas_used, output } => {
                            let revert_reason = if output.len() >= 4 {
                                if let Ok(decoded) = String::from_utf8(output[4..].to_vec()) {
                                    format!("Revert: {}", decoded.trim_end_matches('\0'))
                                } else {
                                    format!("Revert: 0x{}", hex::encode(&output))
                                }
                            } else {
                                "Revert: Unknown".to_string()
                            };
                            (false, Some(revert_reason))
                        }
                        ExecutionResult::Halt { gas_used, reason } => {
                            let halt_reason = format!("Halt: {:?}", reason);
                            (false, Some(halt_reason))
                        }
                    }
                }
                Err(e) => {
                    let error_msg = format!("Execution error: {:?}", e);
                    (false, Some(error_msg))
                }
            }
        } else {
            (false, Some("Database not initialized".to_string()))
        }
    }

    pub fn get_benchmark_report(&self) -> String {
        self.metrics.generate_benchmark_report()
    }
}

// ============================================================================
// MAIN FUNCTION
// ============================================================================

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 === OPTIMIZED ARBITRUM SIMULATOR ===");
    println!("🔧 Mode: Persistent state with smart caching");

    // Create optimized simulator
    let mut simulator = OptimizedArbitrumSimulator::new("wss://lingering-flashy-lambo.arbitrum-mainnet.quiknode.pro/3b4184e83918c9bb09a12af99065f77188c85fd6/").await?;

    // Start WebSocket server
    let listener = TcpListener::bind("127.0.0.1:9999").await?;
    println!("🌍 Server started on 127.0.0.1:9999");
    println!("📡 Ready for feed transactions");

    while let Ok((stream, addr)) = listener.accept().await {
        println!("🔌 New connection: {}", addr);

        let ws_stream = accept_async(stream).await?;
        let (mut ws_sender, mut ws_receiver) = ws_stream.split();

        while let Some(msg) = ws_receiver.next().await {
            match msg? {
                Message::Text(text) => {
                    if let Ok(block_data) = serde_json::from_str::<BlockData>(&text) {
                        match simulator.simulate_block(block_data).await {
                            Ok(result) => {
                                let response = serde_json::to_string(&result)?;
                                ws_sender.send(Message::Text(response)).await?;

                                // Generate detailed benchmark report after 1000+ transactions
                                if simulator.metrics.total_simulations >= 1000 && simulator.metrics.total_simulations % 1000 == 0 {
                                    let report = simulator.get_benchmark_report();
                                    println!("\n{}", report);
                                } else if simulator.metrics.total_simulations > 0 && simulator.metrics.total_simulations % 100 == 0 {
                                    println!("📊 Progress - Total TXs: {}, Success Rate: {:.1}%",
                                        simulator.metrics.total_simulations,
                                        (simulator.metrics.successful_simulations as f64 / simulator.metrics.total_simulations as f64) * 100.0
                                    );
                                }
                            }
                            Err(e) => {
                                println!("❌ Simulation error: {}", e);
                            }
                        }
                    }
                }
                Message::Close(_) => break,
                _ => {}
            }
        }
    }

    Ok(())
}
