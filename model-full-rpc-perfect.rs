use std::sync::Arc;
use revm::db::{CacheDB, EthersDB};
use revm::primitives::{AccountInfo, B256, U256, Address, TransactTo, TxEnv, Env, BlockEnv};
use revm::{Database, Evm};
use ethers::providers::{Provider, Ws};
use serde::{Deserialize, Serialize};
use tokio_tungstenite::{accept_async, tungstenite::Message};
use tokio::net::TcpListener;
use futures_util::{SinkExt, StreamExt};
use std::str::FromStr;
use revm::primitives::Bytes;

// ============================================================================
// SIMPLE STRUCTURES FOR TRANSACTIONS
// ============================================================================

#[derive(Debug, Deserialize)]
struct FeedTransaction {
    hash: String,
    from: Option<String>,
    to: String,
    value: String,
    data: String,
    gas_limit: Option<String>,
    gas_price: Option<String>,
    max_priority_fee: Option<String>,
    nonce: Option<String>,
}

#[derive(Debug, Deserialize)]
struct BlockData {
    block_number: u64,
    transactions: Vec<FeedTransaction>,
}

#[derive(Debug, Serialize)]
struct BlockSimulationResult {
    block_number: u64,
    total_transactions: usize,
    successful_transactions: usize,
    total_time_ms: f64,
    average_time_us: f64,
}

// ============================================================================
// SIMPLE ARBITRUM SIMULATOR - RPC ONLY
// ============================================================================

struct SimpleArbitrumSimulator {
    provider: Arc<Provider<Ws>>,
    cache_db: Option<CacheDB<EthersDB<Provider<Ws>>>>,
}

impl SimpleArbitrumSimulator {
    pub async fn new(rpc_url: &str) -> Result<Self, Box<dyn std::error::Error>> {
        println!("🔗 Connecting to Arbitrum node: {}", rpc_url);
        let provider = Provider::<Ws>::connect(rpc_url).await?;
        let provider = Arc::new(provider);
        
        println!("✅ Connection successful - Pure RPC mode (no bypass)");
        
        Ok(Self {
            provider,
            cache_db: None,
        })
    }

    pub async fn simulate_block(
        &mut self,
        block_data: BlockData,
    ) -> Result<BlockSimulationResult, Box<dyn std::error::Error>> {
        let block_number = block_data.block_number;
        
        println!("\n╭─ Block {} ─ {} transactions ─────────────────────", 
            block_number, block_data.transactions.len());
        
        // ✅ CREATE A FRESH DB for each block (clean state)
        let fork_block = block_number.saturating_sub(1);
        let ethers_db = EthersDB::new(self.provider.clone(), Some(fork_block.into()))
            .ok_or("Failed to create EthersDB")?;
        let mut cache_db = CacheDB::new(ethers_db);
        
        println!("│  🌍 Fork: block {} (Pure RPC, no cache)", fork_block);
        
        // ✅ SIMULATION: Each transaction with real RPC
        let mut successful_transactions = 0;
        let simulation_start = std::time::Instant::now();
        
        for (i, tx) in block_data.transactions.iter().enumerate() {
            println!("├─ TX {}/{} ", i+1, block_data.transactions.len());
            
            let tx_start = std::time::Instant::now();
            let success = self.simulate_single_transaction(&mut cache_db, tx, block_number).await.unwrap_or(false);
            let tx_time = tx_start.elapsed();
            
            if success {
                successful_transactions += 1;
                println!("│  ✅ SUCCESS in {:.1}µs", tx_time.as_nanos() as f64 / 1000.0);
            } else {
                println!("│  ❌ FAILED in {:.1}µs", tx_time.as_nanos() as f64 / 1000.0);
            }
        }
        
        let simulation_time = simulation_start.elapsed();
        
        let result = BlockSimulationResult {
            block_number,
            total_transactions: block_data.transactions.len(),
            successful_transactions,
            total_time_ms: simulation_time.as_millis() as f64,
            average_time_us: if block_data.transactions.len() > 0 {
                (simulation_time.as_nanos() as f64 / 1000.0) / block_data.transactions.len() as f64
            } else {
                0.0
            },
        };
        
        println!("├─ RESULTS ────────────────────────────────────────");
        println!("│  ⚡ Total: {:.2}ms", result.total_time_ms);
        println!("│  📊 Average: {:.1}µs/TX", result.average_time_us);
        println!("│  ✅ Success: {}/{} ({:.1}%)", 
            result.successful_transactions, 
            result.total_transactions,
            (result.successful_transactions as f64 / result.total_transactions as f64) * 100.0
        );
        println!("╰─────────────────────────────────────────────────");
        
        Ok(result)
    }

    async fn simulate_single_transaction(
        &mut self,
        cache_db: &mut CacheDB<EthersDB<Provider<Ws>>>,
        tx: &FeedTransaction,
        block_number: u64,
    ) -> Result<bool, Box<dyn std::error::Error>> {
        
        // ═══════════════════════════════════════════════════════════════════
        println!("    📋 TX: {}", tx.hash);
        println!("    🎯 Target: {}", tx.to);
        println!("    💾 Data: {} bytes", tx.data.len());
        // ═══════════════════════════════════════════════════════════════════
        
        // ✅ SIMPLE PARSING
        let to_address = Address::from_str(&tx.to)?;
        let caller_address = if let Some(from) = &tx.from {
            Address::from_str(from)?
        } else {
            return Err("Missing from address".into());
        };
        
        let calldata_bytes = if tx.data.starts_with("0x") && tx.data.len() > 2 {
            Bytes::from(hex::decode(&tx.data[2..])?)
        } else {
            Bytes::new()
        };
        
        let value = if tx.value == "0" || tx.value.is_empty() {
            U256::ZERO
        } else {
            U256::from_str(&tx.value)?
        };
        
        // 🚀 USE REAL VALUES FROM JAVASCRIPT FEED
        let gas_limit = tx.gas_limit.as_ref()
            .and_then(|g| g.parse::<u64>().ok())
            .unwrap_or(800_000);
            
        let gas_price = tx.gas_price.as_ref()
            .and_then(|g| U256::from_str(g).ok())
            .unwrap_or(U256::from(1_000_000_000u64)); // 1 gwei fallback
            
        let gas_priority_fee = tx.max_priority_fee.as_ref()
            .and_then(|g| U256::from_str(g).ok());
            
        // 🔍 DEBUG: Display used values
        println!("    📊 TX PARAMETERS:");
        println!("       gas_limit: {}", gas_limit);
        println!("       gas_price: {}", gas_price);
        if let Some(priority_fee) = gas_priority_fee {
            println!("       priority_fee: {}", priority_fee);
        }
        
        let tx_nonce = tx.nonce.as_ref()
            .and_then(|n| n.parse::<u64>().ok());
            
        if let Some(nonce) = tx_nonce {
            println!("       nonce: {}", nonce);
        }
        
        // 🔧 SYNC ACCOUNT STATE with TX parameters
        if let Some(tx_nonce) = tx_nonce {
            // Get current account state
            if let Ok(Some(mut account_info)) = cache_db.basic(caller_address) {
                let state_nonce = account_info.nonce;
                
                if state_nonce != tx_nonce {
                    println!("    🔧 NONCE SYNC: state={}, tx={} - Adjustment needed", state_nonce, tx_nonce);
                    
                    // 🔥 ADJUST ACCOUNT NONCE to match TX
                    account_info.nonce = tx_nonce;
                    
                    // 🔥 ADJUST BALANCE if too low to cover fees
                    let max_fee = U256::from(gas_limit) * gas_price;
                    if account_info.balance < max_fee {
                        println!("    💰 BALANCE BOOST: {} → {} (to cover fees)", account_info.balance, max_fee * U256::from(2));
                        account_info.balance = max_fee * U256::from(2); // 2x fees to be safe
                    }
                    
                    // Save modifications
                    cache_db.insert_account_info(caller_address, account_info);
                } else {
                    println!("    ✅ NONCE OK: {}", state_nonce);
                }
            } else {
                println!("    ⚠️ Account not found: {}", caller_address);
            }
        }

        // ✅ STANDARD EVM CONFIGURATION
        let mut env = Env::default();
        env.cfg.chain_id = 42161;
        
        env.tx = TxEnv {
            caller: caller_address,
            gas_limit,
            gas_price,
            transact_to: TransactTo::Call(to_address),
            value,
            data: calldata_bytes,
            nonce: tx_nonce,
            chain_id: Some(42161),
            access_list: vec![],
            gas_priority_fee,
            blob_hashes: vec![],
            max_fee_per_blob_gas: None,
            authorization_list: None,
        };
        
        env.block = BlockEnv {
            number: U256::from(block_number),
            timestamp: U256::from(chrono::Utc::now().timestamp() as u64),
            gas_limit: U256::MAX,
            difficulty: U256::ZERO,
            prevrandao: Some(B256::ZERO),
            basefee: U256::from(10_000_000u64), // 0.01 Gwei
            coinbase: Address::ZERO,
            blob_excess_gas_and_price: Some(revm::primitives::BlobExcessGasAndPrice::new(0)),
        };
        
        // ✅ PURE RPC SIMULATION - NO BYPASS
        let mut evm = Evm::builder()
            .with_db(cache_db)
            .with_env(Box::new(env))
            .build();

        match evm.transact() {
            Ok(res) => {
                match &res.result {
                    revm::primitives::ExecutionResult::Success { gas_used, .. } => {
                        println!("    ✅ SUCCESS - Gas: {}", gas_used);
                        Ok(true)
                    }
                    revm::primitives::ExecutionResult::Revert { gas_used, output } => {
                        let revert_reason = if output.len() >= 3 && output.as_ref() == [0x08, 0x00, 0x00] {
                            "REVERT_0x080000".to_string()
                        } else if output.len() >= 4 {
                            if let Ok(decoded) = String::from_utf8(output[4..].to_vec()) {
                                format!("'{}'", decoded.trim_end_matches('\0'))
                            } else {
                                format!("0x{}", hex::encode(&output))
                            }
                        } else {
                            format!("0x{}", hex::encode(&output))
                        };
                        
                        println!("    ❌ REVERT - Gas: {} - Reason: {}", gas_used, revert_reason);
                        Ok(false)
                    }
                    revm::primitives::ExecutionResult::Halt { gas_used, reason } => {
                        println!("    🛑 HALT - Gas: {} - Reason: {:?}", gas_used, reason);
                        Ok(false)
                    }
                }
            }
            Err(e) => {
                println!("    💥 ERROR: {:?}", e);
                Err(e.into())
            }
        }
    }
}

// ============================================================================
// SIMPLE WEBSOCKET SERVER
// ============================================================================

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🌍 === SIMPLE ARBITRUM SIMULATOR ===");
    println!("🔧 Mode: Pure RPC (no bypass, no smart cache)");
    
    // Create simulator
    let mut simulator = SimpleArbitrumSimulator::new("wss://lingering-flashy-lambo.arbitrum-mainnet.quiknode.pro/3b4184e83918c9bb09a12af99065f77188c85fd6/").await?;
    
    // Start WebSocket server
    let listener = TcpListener::bind("127.0.0.1:9999").await?;
    println!("🌍 Server started on 127.0.0.1:9999");
    println!("📡 Ready for feed transactions");
    
    while let Ok((stream, addr)) = listener.accept().await {
        println!("🔌 New connection: {}", addr);
        
        let ws_stream = accept_async(stream).await?;
        let (mut ws_sender, mut ws_receiver) = ws_stream.split();
        
        while let Some(msg) = ws_receiver.next().await {
            match msg? {
                Message::Text(text) => {
                    if let Ok(block_data) = serde_json::from_str::<BlockData>(&text) {
                        match simulator.simulate_block(block_data).await {
                            Ok(result) => {
                                let response = serde_json::to_string(&result)?;
                                ws_sender.send(Message::Text(response)).await?;
                            }
                            Err(e) => {
                                println!("❌ Simulation error: {}", e);
                            }
                        }
                    }
                }
                Message::Close(_) => break,
                _ => {}
            }
        }
    }
    
    Ok(())
} 